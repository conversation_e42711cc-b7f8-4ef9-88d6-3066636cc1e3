#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数字提取功能的修改
验证对于带后缀的运营编码，只提取主体部分的数字
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from core.file_manager import FileManager
from core.allocation_engine import AllocationEngine
from core.data_processor import DataProcessor
from core.excel_generator import ExcelGenerator


def test_digit_extraction():
    """测试数字提取功能"""
    
    # 测试用例
    test_cases = [
        # (输入编码, 期望输出, 描述)
        ("ZDDLLS-3740581615068381598-B1", "3740581615068381598", "带B1后缀的编码"),
        ("ZDDLLS-3740581615068381598-A1", "3740581615068381598", "带A1后缀的编码"),
        ("DLS-10029969879149-A2", "10029969879149", "带A2后缀的编码"),
        ("GL-123456789-C", "123456789", "带单字母后缀的编码"),
        ("ZDDGL-987654321", "987654321", "无后缀的编码"),
        ("JSB-10026890480657-B3", "10026890480657", "带B3后缀的编码"),
        ("ABC-XYZ123456789-D1", "123456789", "主体部分包含字母的编码"),
    ]
    
    # 创建测试对象
    file_manager = FileManager()
    allocation_engine = AllocationEngine()
    # DataProcessor 需要参数，我们直接测试其方法
    data_processor = DataProcessor.__new__(DataProcessor)  # 创建实例但不调用 __init__
    excel_generator = ExcelGenerator()
    
    print("=" * 60)
    print("测试数字提取功能修改")
    print("=" * 60)
    
    all_passed = True
    
    for i, (input_code, expected, description) in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {description}")
        print(f"输入编码: {input_code}")
        print(f"期望输出: {expected}")
        
        # 测试各个模块的函数
        results = {
            "FileManager": file_manager.extract_digits_from_code(input_code),
            "AllocationEngine": allocation_engine.extract_digits_from_code(input_code),
            "DataProcessor": data_processor.extract_digits_from_code(input_code),
            "ExcelGenerator": excel_generator.extract_digits_from_code(input_code),
        }
        
        # 检查结果
        test_passed = True
        for module_name, result in results.items():
            print(f"{module_name}: {result}")
            if result != expected:
                print(f"  ❌ {module_name} 结果不匹配！期望: {expected}, 实际: {result}")
                test_passed = False
                all_passed = False
        
        if test_passed:
            print("  ✅ 所有模块测试通过")
        else:
            print("  ❌ 部分模块测试失败")
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有测试用例通过！数字提取功能修改成功。")
    else:
        print("❌ 部分测试用例失败，需要检查代码。")
    print("=" * 60)
    
    return all_passed


if __name__ == "__main__":
    test_digit_extraction()
